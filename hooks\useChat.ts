'use client';

import { useState, useCallback, useEffect } from 'react';
import { AIClient, AI_PROVIDERS } from '@/lib/ai-client.js';

export interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  timestamp: number;
  lastMessage: string;
  messageCount: number;
}

export interface ChatState {
  conversations: Conversation[];
  currentConversationId: string | null;
  isLoading: boolean;
  error: string | null;
  provider: string;
  apiKey: string;
}

const STORAGE_KEY = 'chatgpt-clone-data';

export function useChat() {
  const [state, setState] = useState<ChatState>({
    conversations: [],
    currentConversationId: null,
    isLoading: false,
    error: null,
    provider: 'pollinations',
    apiKey: '',
  });

  const [aiClient, setAiClient] = useState<AIClient | null>(null);

  // Load data from localStorage on mount
  useEffect(() => {
    const savedData = localStorage.getItem(STORAGE_KEY);
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData);
        setState(prev => ({
          ...prev,
          conversations: parsed.conversations || [],
          currentConversationId: parsed.currentConversationId || null,
          provider: parsed.provider || 'pollinations',
          apiKey: parsed.apiKey || '',
        }));
      } catch (error) {
        console.error('Failed to load saved data:', error);
      }
    }
  }, []);

  // Save data to localStorage whenever state changes
  useEffect(() => {
    const dataToSave = {
      conversations: state.conversations,
      currentConversationId: state.currentConversationId,
      provider: state.provider,
      apiKey: state.apiKey,
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
  }, [state.conversations, state.currentConversationId, state.provider, state.apiKey]);

  // Initialize AI client when provider or API key changes
  useEffect(() => {
    try {
      const client = new AIClient(state.provider, state.apiKey || undefined);
      setAiClient(client);
    } catch (error) {
      console.error('Failed to initialize AI client:', error);
      setState(prev => ({ ...prev, error: 'Failed to initialize AI client' }));
    }
  }, [state.provider, state.apiKey]);

  const getCurrentConversation = useCallback((): Conversation | null => {
    if (!state.currentConversationId) return null;
    return state.conversations.find(c => c.id === state.currentConversationId) || null;
  }, [state.conversations, state.currentConversationId]);

  const createNewConversation = useCallback((): string => {
    const id = generateMessageId();
    const newConversation: Conversation = {
      id,
      title: 'New Conversation',
      messages: [],
      timestamp: Date.now(),
      lastMessage: '',
      messageCount: 0,
    };

    setState(prev => ({
      ...prev,
      conversations: [newConversation, ...prev.conversations],
      currentConversationId: id,
    }));

    return id;
  }, []);

  const selectConversation = useCallback((id: string) => {
    setState(prev => ({ ...prev, currentConversationId: id }));
  }, []);

  const deleteConversation = useCallback((id: string) => {
    setState(prev => {
      const newConversations = prev.conversations.filter(c => c.id !== id);
      const newCurrentId = prev.currentConversationId === id 
        ? (newConversations.length > 0 ? newConversations[0].id : null)
        : prev.currentConversationId;

      return {
        ...prev,
        conversations: newConversations,
        currentConversationId: newCurrentId,
      };
    });
  }, []);

  const updateConversationTitle = useCallback((id: string, title: string) => {
    setState(prev => ({
      ...prev,
      conversations: prev.conversations.map(c =>
        c.id === id ? { ...c, title } : c
      ),
    }));
  }, []);

  const addMessage = useCallback((conversationId: string, message: Message) => {
    setState(prev => ({
      ...prev,
      conversations: prev.conversations.map(c => {
        if (c.id === conversationId) {
          const newMessages = [...c.messages, message];
          return {
            ...c,
            messages: newMessages,
            lastMessage: message.content.slice(0, 100),
            messageCount: newMessages.length,
            timestamp: Date.now(),
          };
        }
        return c;
      }),
    }));
  }, []);

  const updateLastMessage = useCallback((conversationId: string, content: string) => {
    setState(prev => ({
      ...prev,
      conversations: prev.conversations.map(c => {
        if (c.id === conversationId && c.messages.length > 0) {
          const newMessages = [...c.messages];
          const lastMessage = newMessages[newMessages.length - 1];
          if (lastMessage.role === 'assistant') {
            newMessages[newMessages.length - 1] = { ...lastMessage, content };
          }
          return {
            ...c,
            messages: newMessages,
            lastMessage: content.slice(0, 100),
          };
        }
        return c;
      }),
    }));
  }, []);

  const sendMessage = useCallback(async (content: string) => {
    if (!aiClient) {
      setState(prev => ({ ...prev, error: 'AI client not initialized' }));
      return;
    }

    let conversationId = state.currentConversationId;
    if (!conversationId) {
      conversationId = createNewConversation();
    }

    const userMessage = createMessage('user', content);
    addMessage(conversationId, userMessage);

    // Auto-generate title for new conversations
    const conversation = state.conversations.find(c => c.id === conversationId);
    if (conversation && conversation.title === 'New Conversation' && content.length > 0) {
      const title = content.slice(0, 50) + (content.length > 50 ? '...' : '');
      updateConversationTitle(conversationId, title);
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const currentConv = state.conversations.find(c => c.id === conversationId);
      const messages = currentConv ? [...currentConv.messages, userMessage] : [userMessage];

      const assistantMessage = createMessage('assistant', '');
      addMessage(conversationId, assistantMessage);

      let fullResponse = '';
      const stream = aiClient.createChatCompletionStream({
        messages: messages.map(m => ({ role: m.role, content: m.content })),
        stream: true,
      });

      for await (const chunk of stream) {
        const delta = chunk.choices[0]?.delta?.content || '';
        if (delta) {
          fullResponse += delta;
          updateLastMessage(conversationId, fullResponse);
        }
      }

    } catch (error) {
      console.error('Error sending message:', error);
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'An error occurred' 
      }));
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [aiClient, state.currentConversationId, state.conversations, createNewConversation, addMessage, updateConversationTitle, updateLastMessage]);

  const setProvider = useCallback((provider: string) => {
    setState(prev => ({ ...prev, provider }));
  }, []);

  const setApiKey = useCallback((apiKey: string) => {
    setState(prev => ({ ...prev, apiKey }));
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  const exportConversations = useCallback(() => {
    const dataStr = JSON.stringify(state.conversations, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `chatgpt-clone-conversations-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  }, [state.conversations]);

  const importConversations = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const imported = JSON.parse(e.target?.result as string);
        if (Array.isArray(imported)) {
          setState(prev => ({
            ...prev,
            conversations: [...imported, ...prev.conversations],
          }));
        }
      } catch (error) {
        console.error('Failed to import conversations:', error);
        setState(prev => ({ ...prev, error: 'Failed to import conversations' }));
      }
    };
    reader.readAsText(file);
  }, []);

  return {
    ...state,
    currentConversation: getCurrentConversation(),
    createNewConversation,
    selectConversation,
    deleteConversation,
    updateConversationTitle,
    sendMessage,
    setProvider,
    setApiKey,
    clearError,
    exportConversations,
    importConversations,
  };
}
